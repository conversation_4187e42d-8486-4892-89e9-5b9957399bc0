* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fdf2e9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
header {
  background-color: #fdf2e9;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.logo img {
  height: 50px;
  transition: transform 0.3s ease;
}

.logo img:hover {
  transform: scale(1.05);
}

nav ul {
  display: flex;
  list-style: none;
}

nav ul li {
  margin-left: 20px;
}

nav ul li a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
  position: relative;
  transition: color 0.3s ease;
}

nav ul li a:hover {
  color: #e67e22;
}

nav ul li a:not(.cta-nav):after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #e67e22;
  transition: width 0.3s ease;
}

nav ul li a:not(.cta-nav):hover:after {
  width: 100%;
}

.cta-nav {
  background-color: #e67e22;
  color: #fff !important;
  padding: 8px 16px;
  border-radius: 5px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.cta-nav:hover {
  background-color: #cf711f;
  color: #fff !important;
  transform: translateY(-3px);
}

.mobile-nav-btn {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #333;
  cursor: pointer;
}

/* Hero Section - Fullscreen */
.fullscreen {
  height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
    url("../img/hero-bg.jpg");
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
  position: relative;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.hero .container {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content {
  padding: 0 20px;
}

.hero h1 {
  font-size: 4.4rem;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero p {
  font-size: 1.6rem;
  margin-bottom: 20px;
  line-height: 1.6;
}

.hero-btns {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

/* Programs Section */
.programs {
  padding: 80px 0;
  background-color: #fff;
  position: relative;
  z-index: 1;
}

.programs h2 {
  text-align: center;
  margin-bottom: 50px;
  color: #333;
  font-size: 2.5rem;
  position: relative;
  padding-bottom: 15px;
}

.programs h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #e67e22;
}

.programs-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.program-item {
  background-color: #fdf2e9;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(230, 126, 34, 0.1);
}

.program-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  border-color: rgba(230, 126, 34, 0.3);
}

.program-icon {
  font-size: 3rem;
  color: #e67e22;
  margin-bottom: 20px;
  text-align: center;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.program-item h3 {
  margin-bottom: 15px;
  font-size: 1.5rem;
  color: #333;
  text-align: center;
  position: relative;
  padding-bottom: 10px;
}

.program-item h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background-color: #e67e22;
}

.program-item p {
  margin-bottom: 20px;
  line-height: 1.6;
  color: #555;
}

.program-features {
  list-style: none;
  padding: 0;
}

.program-features li {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.program-features li i {
  color: #e67e22;
  margin-right: 10px;
  font-size: 1.1rem;
}

/* CTA Banner */
.cta-banner {
  background: linear-gradient(to right, #e67e22, #cf711f);
  color: #fff;
  padding: 60px 0;
  text-align: center;
}

.cta-banner h2 {
  font-size: 2rem;
  margin-bottom: 15px;
}

/* Intro Section */
.intro {
  padding: 80px 0;
  background-color: #fff;
}

.intro h2 {
  text-align: center;
  margin-bottom: 40px;
  color: #333;
}

.intro-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.intro-item {
  background-color: #fdf2e9;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.intro-item:hover {
  transform: translateY(-5px);
}

.intro-item i {
  font-size: 2rem;
  color: #e67e22;
  margin-bottom: 15px;
}

.intro-item h3 {
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.btn-more {
  display: inline-block;
  margin-top: 15px;
  color: #e67e22;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.btn-more:hover {
  color: #cf711f;
}

/* Stats Section */
.stats {
  background-color: #fae5d3;
  padding: 80px 0;
}

.stats h2 {
  text-align: center;
  margin-bottom: 50px;
  color: #333;
  font-size: 2.2rem;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.stat-item {
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s;
}

.stat-item:hover {
  transform: translateY(-10px);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #e67e22;
  margin-bottom: 15px;
  transition: color 0.3s ease, transform 0.3s ease;
}

.stat-item:hover .stat-number {
  transform: scale(1.1);
}

.stat-label {
  font-size: 1.1rem;
  color: #555;
}

/* Benefits Section */
.benefits {
  padding: 80px 0;
  background-color: #fff;
}

.benefits h2 {
  text-align: center;
  margin-bottom: 50px;
  color: #333;
  font-size: 2.2rem;
}

.benefits-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 30px;
}

.benefit-item {
  background-color: #fdf2e9;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.benefit-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.benefit-item i {
  font-size: 3rem;
  color: #e67e22;
  margin-bottom: 20px;
}

.benefit-item h3 {
  margin-bottom: 15px;
  font-size: 1.3rem;
  color: #333;
}

.benefit-item p {
  color: #555;
  line-height: 1.6;
}

/* FAQ Section */
.faq {
  background-color: #fae5d3;
  padding: 80px 0;
}

.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background-color: #fff;
  margin-bottom: 15px;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.faq-question {
  padding: 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-question h3 {
  font-size: 1.1rem;
  font-weight: 500;
}

.faq-answer {
  padding: 0 20px;
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease-out;
}

.faq-item.active .faq-answer {
  padding: 0 20px 20px;
  height: auto;
}

.faq-item.active .fa-chevron-down {
  transform: rotate(180deg);
}

.fa-chevron-down {
  transition: transform 0.3s;
  color: #e67e22;
}

/* Contact Section */
.contact {
  padding: 80px 0;
  background-color: #fae5d3;
}

.contact h2 {
  text-align: center;
  margin-bottom: 50px;
  color: #333;
  font-size: 2.2rem;
  position: relative;
  padding-bottom: 15px;
}

.contact h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #e67e22;
}

.contact-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.contact-card {
  background-color: #fff;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  max-width: 600px;
  width: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.contact-image {
  background-color: #e67e22;
  color: #fff;
  padding: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-image i {
  font-size: 5rem;
}

.contact-info {
  padding: 30px 30px 30px 30px;
  flex: 1;
}

.contact-info h3 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 5px;
}

.contact-title {
  color: #e67e22;
  font-weight: 500;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.contact-details {
  list-style: none;
  padding: 0;
  margin-bottom: 25px;
}

.contact-details li {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  transition: transform 0.3s ease;
}

.contact-icon img {
  width: 32px;
  height: 32px;
}

.contact-icon:hover {
  transform: scale(1.1);
}

.contact-text {
  color: #555;
  font-size: 1.1rem;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-text:hover {
  color: #e67e22;
}

/* Footer */
footer {
  background-color: #333;
  color: #fff;
  padding: 40px 0 20px;
  text-align: center;
}

.footer-logo {
  margin-bottom: 20px;
}

.footer-logo img {
  height: 60px;
  margin-bottom: 15px;
}

.footer-bottom {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animation */
.section-hidden {
  opacity: 0;
  transform: translateY(8rem);
  transition: all 1s;
}

/* Scroll Button Animation */
.scroll-btn {
  position: relative;
  overflow: hidden;
  animation: bounce 2s infinite;
}

.scroll-btn:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(-100%);
  transition: transform 0.6s ease-in-out;
}

.scroll-btn:hover:after {
  transform: translateX(0);
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Sticky Header */
header.sticky {
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Animation for FAQ */
.faq-question i {
  transition: transform 0.3s;
}

.faq-item.active .faq-question i {
  transform: rotate(180deg);
}

/* Cải thiện nút Tìm hiểu thêm */
.btn-primary {
  display: inline-block;
  background-color: #e67e22;
  color: #fff;
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 500;
  padding: 15px 30px;
  border-radius: 50px;
  transition: all 0.3s;
  box-shadow: 0 4px 10px rgba(230, 126, 34, 0.3);
}

.btn-primary:hover {
  background-color: #cf711f;
  transform: translateY(-3px);
  box-shadow: 0 7px 15px rgba(230, 126, 34, 0.4);
}

.btn-primary:active {
  transform: translateY(-1px);
}

.btn {
  display: inline-block;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 5px;
  transition: all 0.3s;
}

.btn-secondary {
  background-color: #fff;
  color: #555;
  border: 1px solid #e67e22;
}

.btn-secondary:hover {
  background-color: #fdf2e9;
  color: #e67e22;
}

.btn-contact {
  display: inline-block;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 5px;
  transition: all 0.3s;
  margin-top: 10px;
  text-align: center;
}

.btn-contact.phone {
  background-color: #e67e22;
  color: #fff;
}

.btn-contact.zalo {
  background-color: #0068ff;
  color: #fff;
}

.btn-contact.email {
  background-color: #27ae60;
  color: #fff;
}

.btn-contact:hover {
  transform: translateY(-3px);
}

/* Đảm bảo các icon Font Awesome hiển thị đúng */
.fa-child,
.fa-school,
.fa-check,
.fa-book-open,
.fa-chalkboard-teacher,
.fa-building,
.fa-hands-helping,
.fa-book,
.fa-headset {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  display: inline-block;
}
