// ===========================
// NOVASTARS LANDING PAGE JS
// Based on Template Structure
// ===========================

// Smooth scrolling cho navigation links
document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  anchor.addEventListener("click", function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute("href"));
    if (target) {
      const headerHeight = document.querySelector("header").offsetHeight;
      const targetPosition = target.offsetTop - headerHeight;

      window.scrollTo({
        top: targetPosition,
        behavior: "smooth",
      });
    }
  });
});

// Header scroll effect
window.addEventListener("scroll", function () {
  const header = document.querySelector("header");
  if (window.scrollY > 50) {
    header.style.backgroundColor = "rgba(253, 242, 233, 0.95)";
    header.style.backdropFilter = "blur(10px)";
    header.style.boxShadow = "0 4px 20px rgba(0, 0, 0, 0.15)";
  } else {
    header.style.backgroundColor = "#fdf2e9";
    header.style.backdropFilter = "none";
    header.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.1)";
  }
});

// Mobile navigation toggle
const mobileNavBtn = document.querySelector(".mobile-nav-btn");
const nav = document.querySelector("nav");

if (mobileNavBtn && nav) {
  mobileNavBtn.addEventListener("click", function () {
    const isExpanded = this.getAttribute("aria-expanded") === "true";
    this.setAttribute("aria-expanded", !isExpanded);
    nav.classList.toggle("nav-open");

    // Change icon
    const icon = this.querySelector("i");
    if (nav.classList.contains("nav-open")) {
      icon.className = "fas fa-times";
    } else {
      icon.className = "fas fa-bars";
    }
  });
}

// Intersection Observer for animations
const observerOptions = {
  threshold: 0.1,
  rootMargin: "0px 0px -50px 0px",
};

const observer = new IntersectionObserver(function (entries) {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      entry.target.style.opacity = "1";
      entry.target.style.transform = "translateY(0)";
    }
  });
}, observerOptions);

// Initialize animations on page load
document.addEventListener("DOMContentLoaded", function () {
  // Add initial styles for animation
  const animatedElements = document.querySelectorAll(
    ".benefit-group, .stat-item, .skill-group, .survey-img"
  );

  animatedElements.forEach((element) => {
    element.style.opacity = "0";
    element.style.transform = "translateY(30px)";
    element.style.transition = "opacity 0.8s ease, transform 0.8s ease";
    observer.observe(element);
  });

  // CTA button tracking
  const ctaButton = document.querySelector(".btn-cta");
  if (ctaButton) {
    ctaButton.addEventListener("click", function (e) {
      console.log("User clicked CTA button - redirecting to partner page");
      // Add analytics tracking here if needed
      // gtag('event', 'click', { 'event_category': 'cta', 'event_label': 'partner_registration' });
    });
  }

  // Scroll button in hero
  const scrollBtn = document.querySelector(".scroll-btn");
  if (scrollBtn) {
    scrollBtn.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        const headerHeight = document.querySelector("header").offsetHeight;
        const targetPosition = target.offsetTop - headerHeight;

        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    });
  }

  // Carousel auto-scroll pause on hover
  const carouselTrack = document.querySelector(".carousel-track");
  if (carouselTrack) {
    carouselTrack.addEventListener("mouseenter", function () {
      this.style.animationPlayState = "paused";
    });

    carouselTrack.addEventListener("mouseleave", function () {
      this.style.animationPlayState = "running";
    });

    // Add click event for carousel images
    const carouselImages = document.querySelectorAll(".carousel-img");
    carouselImages.forEach((img, index) => {
      img.addEventListener("click", function () {
        console.log(`Clicked on activity image ${index + 1}`);
        // You can add lightbox or modal functionality here
      });
    });
  }

  console.log("Novastars Landing Page - Loaded successfully!");
});
