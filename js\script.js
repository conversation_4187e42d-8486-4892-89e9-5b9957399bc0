// JavaScript cơ bản cho trang web Nova Stars

// Smooth scrolling cho navigation links
document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  anchor.addEventListener("click", function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute("href"));
    if (target) {
      target.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  });
});

// Xử lý form liên hệ
document
  .getElementById("contactForm")
  .addEventListener("submit", function (e) {
    e.preventDefault();

    // Lấy dữ liệu từ form
    const formData = new FormData(this);
    const data = {};
    for (let [key, value] of formData.entries()) {
      data[key] = value;
    }

    // Hiển thị thông báo thành công
    alert(
      "Cảm ơn bạn đã đăng ký! Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất."
    );

    // Reset form
    this.reset();

    // Trong thực tế, bạn sẽ gửi dữ liệu này đến server
    console.log("Dữ liệu đăng ký:", data);
  });

// Hiệu ứng khi scroll
window.addEventListener("scroll", function () {
  const header = document.querySelector(".header");
  if (window.scrollY > 100) {
    header.style.background = "linear-gradient(135deg, #ff6b6b, #ffa726)";
    header.style.boxShadow = "0 2px 20px rgba(0,0,0,0.2)";
  } else {
    header.style.boxShadow = "0 2px 10px rgba(0,0,0,0.1)";
  }
});

// Animation cho feature cards khi scroll vào view
const observerOptions = {
  threshold: 0.1,
  rootMargin: "0px 0px -50px 0px",
};

const observer = new IntersectionObserver(function (entries) {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      entry.target.style.animation = "fadeInUp 0.6s ease-out forwards";
    }
  });
}, observerOptions);

// Quan sát tất cả feature cards
document.querySelectorAll(".feature-card").forEach((card) => {
  observer.observe(card);
});

// Thêm hiệu ứng loading cho trang
document.addEventListener("DOMContentLoaded", function() {
  // Thêm class loaded để kích hoạt animations
  document.body.classList.add("loaded");
  
  // Log thông tin trang đã load
  console.log("Nova Stars - Trang web đã tải thành công!");
});
