// ===========================
// NOVASTARS LANDING PAGE JS
// ===========================

// Smooth scrolling cho navigation links
document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  anchor.addEventListener("click", function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute("href"));
    if (target) {
      target.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  });
});

// Header scroll effect
window.addEventListener("scroll", function () {
  const header = document.querySelector(".header");
  if (window.scrollY > 50) {
    header.style.background = "rgba(96, 165, 250, 0.95)";
    header.style.backdropFilter = "blur(10px)";
  } else {
    header.style.background =
      "linear-gradient(135deg, var(--accent-blue), var(--accent-blue-light))";
    header.style.backdropFilter = "blur(10px)";
  }
});

// Intersection Observer for animations
const observerOptions = {
  threshold: 0.1,
  rootMargin: "0px 0px -50px 0px",
};

const observer = new IntersectionObserver(function (entries) {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      entry.target.style.opacity = "1";
      entry.target.style.transform = "translateY(0)";
    }
  });
}, observerOptions);

// Observe elements for animation
document.addEventListener("DOMContentLoaded", function () {
  // Add initial styles for animation
  const animatedElements = document.querySelectorAll(
    ".benefit-group, .stat-item, .skill-group, .survey-img"
  );

  animatedElements.forEach((element) => {
    element.style.opacity = "0";
    element.style.transform = "translateY(20px)";
    element.style.transition = "opacity 0.6s ease, transform 0.6s ease";
    observer.observe(element);
  });

  // CTA button tracking
  const ctaButton = document.querySelector(".btn-cta");
  if (ctaButton) {
    ctaButton.addEventListener("click", function (e) {
      console.log("User clicked CTA button - redirecting to partner page");
      // Add analytics tracking here if needed
      // gtag('event', 'click', { 'event_category': 'cta', 'event_label': 'partner_registration' });
    });
  }

  // Carousel auto-scroll pause on hover
  const carouselTrack = document.querySelector(".carousel-track");
  if (carouselTrack) {
    carouselTrack.addEventListener("mouseenter", function () {
      this.style.animationPlayState = "paused";
    });

    carouselTrack.addEventListener("mouseleave", function () {
      this.style.animationPlayState = "running";
    });
  }

  console.log("Novastars Landing Page - Loaded successfully!");
});
