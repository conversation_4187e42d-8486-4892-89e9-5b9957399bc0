/* ===========================
   NOVASTARS LANDING PAGE CSS
   =========================== */

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* CSS Variables */
:root {
  /* Colors - Preschool Friendly Palette */
  --primary-green: #4ade80;
  --primary-green-light: #86efac;
  --primary-green-dark: #22c55e;

  --secondary-orange: #fb923c;
  --secondary-orange-light: #fdba74;
  --secondary-orange-dark: #ea580c;

  --accent-blue: #60a5fa;
  --accent-blue-light: #93c5fd;
  --accent-blue-dark: #3b82f6;

  --accent-yellow: #fbbf24;
  --accent-yellow-light: #fcd34d;

  --text-dark: #1f2937;
  --text-medium: #4b5563;
  --text-light: #6b7280;

  --background-white: #ffffff;
  --background-light: #f9fafb;
  --background-cream: #fefbf7;

  --border-light: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-primary: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;
  --spacing-3xl: 6rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
}

/* Base Styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--background-cream);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-lg);
  }
}

/* Typography */
.section-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-dark);
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  font-weight: 600;
  text-decoration: none;
  border-radius: var(--radius-full);
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-green),
    var(--primary-green-light)
  );
  color: var(--background-white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: linear-gradient(
    135deg,
    var(--primary-green-dark),
    var(--primary-green)
  );
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-cta {
  background: linear-gradient(
    135deg,
    var(--secondary-orange),
    var(--secondary-orange-light)
  );
  color: var(--background-white);
  box-shadow: var(--shadow-md);
  font-size: var(--font-size-xl);
  padding: var(--spacing-lg) var(--spacing-2xl);
}

.btn-cta:hover {
  background: linear-gradient(
    135deg,
    var(--secondary-orange-dark),
    var(--secondary-orange)
  );
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Header */
.header {
  background: linear-gradient(
    135deg,
    var(--accent-blue),
    var(--accent-blue-light)
  );
  color: var(--background-white);
  padding: var(--spacing-sm) 0;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-img {
  height: 40px;
  width: auto;
  transition: transform 0.3s ease;
}

.logo-img:hover {
  transform: scale(1.05);
}

.nav-links {
  display: flex;
  list-style: none;
  gap: var(--spacing-lg);
}

.nav-links a {
  color: var(--background-white);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.nav-links a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-green), var(--accent-blue));
  color: var(--background-white);
  padding: calc(80px + var(--spacing-3xl)) 0 var(--spacing-3xl);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 900px;
  margin: 0 auto;
}

.hero h1 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  line-height: 1.2;
}

.hero-description {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xl);
  opacity: 0.95;
  line-height: 1.7;
}

/* Overview Section */
.overview {
  padding: var(--spacing-3xl) 0;
  background: var(--background-white);
}

.overview-description {
  font-size: var(--font-size-lg);
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.8;
  color: var(--text-medium);
}

/* Benefits Section */
.benefits {
  padding: var(--spacing-3xl) 0;
  background: var(--background-light);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.benefit-group {
  background: var(--background-white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.benefit-group:hover {
  transform: translateY(-5px);
}

.benefit-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--primary-green-dark);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.benefit-list {
  list-style: none;
}

.benefit-list li {
  padding: var(--spacing-xs) 0;
  position: relative;
  padding-left: var(--spacing-lg);
}

.benefit-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--primary-green);
  font-weight: bold;
}

/* Feedback Section */
.feedback {
  padding: var(--spacing-3xl) 0;
  background: var(--background-white);
}

.feedback-group {
  margin-bottom: var(--spacing-2xl);
}

.feedback-subtitle {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.feedback-intro {
  font-size: var(--font-size-lg);
  text-align: center;
  color: var(--text-medium);
  margin-bottom: var(--spacing-xl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-item {
  background: linear-gradient(
    135deg,
    var(--accent-yellow-light),
    var(--accent-yellow)
  );
  padding: var(--spacing-lg);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-3px);
}

.stat-number {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
}

.stat-text {
  font-size: var(--font-size-sm);
  color: var(--text-medium);
  line-height: 1.5;
}

.survey-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.survey-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.survey-img:hover {
  transform: scale(1.05);
}

.feedback-placeholder {
  text-align: center;
  color: var(--text-light);
  font-style: italic;
  padding: var(--spacing-xl);
  background: var(--background-light);
  border-radius: var(--radius-lg);
}

.activities-carousel {
  margin-top: var(--spacing-2xl);
}

.carousel-container {
  overflow: hidden;
  border-radius: var(--radius-lg);
}

.carousel-track {
  display: flex;
  gap: var(--spacing-sm);
  animation: scroll 20s linear infinite;
}

.carousel-img {
  width: 300px;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.carousel-placeholder {
  text-align: center;
  padding: var(--spacing-2xl);
  background: var(--background-light);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-light);
}

.carousel-placeholder p {
  color: var(--text-medium);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-lg);
}

.placeholder-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.placeholder-item {
  background: var(--background-white);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  color: var(--text-medium);
  box-shadow: var(--shadow-sm);
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Program Section */
.program {
  padding: var(--spacing-3xl) 0;
  background: var(--background-light);
}

.program-intro {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.program-intro p {
  font-size: var(--font-size-lg);
  color: var(--text-medium);
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.8;
}

.program-groups {
  display: grid;
  gap: var(--spacing-2xl);
}

.skill-group {
  background: var(--background-white);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.skill-group-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--primary-green-dark);
  margin-bottom: var(--spacing-md);
}

.skill-group-description {
  font-size: var(--font-size-base);
  color: var(--text-medium);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.skill-item {
  background: linear-gradient(
    135deg,
    var(--accent-blue-light),
    var(--accent-blue)
  );
  color: var(--background-white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.specialized-skills {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.specialized-category {
  background: var(--background-light);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
}

.specialized-category h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--secondary-orange-dark);
  margin-bottom: var(--spacing-md);
}

.specialized-category ul {
  list-style: none;
}

.specialized-category li {
  padding: var(--spacing-xs) 0;
  position: relative;
  padding-left: var(--spacing-lg);
}

.specialized-category li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--secondary-orange);
  font-weight: bold;
}

/* CTA Section */
.cta {
  padding: var(--spacing-3xl) 0;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-blue));
  color: var(--background-white);
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-xl);
  line-height: 1.3;
}
