* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fdf2e9;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
header {
  background-color: #fdf2e9;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.logo img {
  height: 50px;
  transition: transform 0.3s ease;
}

.logo img:hover {
  transform: scale(1.05);
}

nav ul {
  display: flex;
  list-style: none;
}

nav ul li {
  margin-left: 20px;
}

nav ul li a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s;
  position: relative;
  transition: color 0.3s ease;
}

nav ul li a:hover {
  color: #e67e22;
}

nav ul li a:not(.cta-nav):after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #e67e22;
  transition: width 0.3s ease;
}

nav ul li a:not(.cta-nav):hover:after {
  width: 100%;
}

.cta-nav {
  background-color: #e67e22;
  color: #fff !important;
  padding: 8px 16px;
  border-radius: 5px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.cta-nav:hover {
  background-color: #cf711f;
  color: #fff !important;
  transform: translateY(-3px);
}

.mobile-nav-btn {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #333;
  cursor: pointer;
}

/* Hero Section */
.hero {
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
    url("../img/background.jpg");
  background-size: cover;
  background-position: center;
  color: #fff;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.hero.fullscreen {
  min-height: 100vh;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.hero .container {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content {
  padding: 0 20px;
}

.hero h1 {
  font-size: 3.2rem;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero p {
  font-size: 1.3rem;
  margin-bottom: 20px;
  line-height: 1.6;
}

.hero-btns {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

/* Buttons */
.btn {
  display: inline-block;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 5px;
  transition: all 0.3s;
}

.btn-primary {
  display: inline-block;
  background-color: #e67e22;
  color: #fff;
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 500;
  padding: 15px 30px;
  border-radius: 50px;
  transition: all 0.3s;
  box-shadow: 0 4px 10px rgba(230, 126, 34, 0.3);
}

.btn-primary:hover {
  background-color: #cf711f;
  transform: translateY(-3px);
  box-shadow: 0 7px 15px rgba(230, 126, 34, 0.4);
}

.btn-primary:active {
  transform: translateY(-1px);
}

/* Overview Section */
.overview {
  padding: 80px 0;
  background: #fff;
}

.overview h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 40px;
  color: #333;
}

.overview-description {
  font-size: 1.2rem;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.8;
  color: #555;
}

/* Benefits Section */
.benefits {
  padding: 80px 0;
  background: #fae5d3;
}

.benefits h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #333;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.benefit-group {
  background: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.benefit-group:hover {
  transform: translateY(-5px);
}

.benefit-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #e67e22;
  margin-bottom: 20px;
  text-align: center;
}

.benefit-list {
  list-style: none;
}

.benefit-list li {
  padding: 8px 0;
  position: relative;
  padding-left: 25px;
}

.benefit-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #e67e22;
  font-weight: bold;
}

/* Feedback Section */
.feedback {
  padding: 80px 0;
  background: #fff;
}

.feedback h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #333;
}

.feedback-group {
  margin-bottom: 60px;
}

.feedback-subtitle {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.feedback-intro {
  font-size: 1.2rem;
  text-align: center;
  color: #555;
  margin-bottom: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  margin-bottom: 40px;
}

.stat-item {
  background: linear-gradient(135deg, #ffd89b, #19547b);
  padding: 25px 15px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-3px);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 8px;
}

.stat-text {
  font-size: 0.85rem;
  color: #fff;
  line-height: 1.4;
}

.survey-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-bottom: 40px;
  padding: 20px 0;
}

.survey-img {
  width: 100%;
  height: 450px;
  object-fit: contain;
  object-position: center;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.5s ease;
  background: #fff;
  cursor: pointer;
}

.survey-img:hover {
  transform: scale(1.15);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  z-index: 100;
  position: relative;
}

.feedback-placeholder {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 10px;
}

/* School Testimonials */
.school-testimonials {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

.testimonial-item {
  background: #fff;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 5px solid #e67e22;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.testimonial-item blockquote {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #333;
  margin-bottom: 20px;
  font-style: italic;
  position: relative;
}

.testimonial-item blockquote::before {
  content: '"';
  font-size: 4rem;
  color: #e67e22;
  position: absolute;
  top: -10px;
  left: -15px;
  font-family: Georgia, serif;
  opacity: 0.3;
}

.testimonial-item cite {
  display: block;
  font-size: 0.95rem;
  color: #666;
  font-weight: 600;
  text-align: right;
  font-style: normal;
}

.activities-carousel {
  margin-top: 60px;
}

.carousel-container {
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.carousel-track {
  display: flex;
  gap: 15px;
  animation: scroll 25s linear infinite;
  width: calc(300px * 9 + 15px * 8); /* 9 images + gaps */
}

.carousel-img {
  width: 300px;
  height: 200px;
  object-fit: cover;
  border-radius: 10px;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.carousel-img:hover {
  transform: scale(1.05);
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(
      calc(-300px * 6 - 15px * 5)
    ); /* Move by 6 original images */
  }
}

/* Program Section */
.program {
  padding: 80px 0;
  background: #fae5d3;
}

.program h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #333;
}

.program-intro {
  text-align: center;
  margin-bottom: 60px;
}

.program-intro p {
  font-size: 1.2rem;
  color: #555;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.8;
}

.program-groups {
  display: grid;
  gap: 40px;
}

.skill-group {
  background: #fff;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Foundation Skills Container - Two Separate Cards */
.foundation-skills-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
}

.foundation-text-card {
  /* Left card - text content */
}

.foundation-skills-card {
  /* Right card - skills list */
}

.foundation-skills-card .skills-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-top: 20px;
}

.foundation-skills-card .skill-item {
  margin: 0;
  text-align: center;
  padding: 12px 8px;
  font-size: 0.9rem;
}

.skill-group-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #e67e22;
  margin-bottom: 20px;
}

.skill-group-description {
  font-size: 1rem;
  color: #555;
  line-height: 1.7;
  margin-bottom: 30px;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.skill-item {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
}

.specialized-skills {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.specialized-category {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
}

.specialized-category h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #e67e22;
  margin-bottom: 15px;
}

.specialized-category ul {
  list-style: none;
}

.specialized-category li {
  padding: 5px 0;
  position: relative;
  padding-left: 20px;
}

.specialized-category li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #e67e22;
  font-weight: bold;
}

/* CTA Section */
.cta {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 30px;
  line-height: 1.3;
}

.btn-cta {
  background: #e67e22;
  color: #fff;
  font-size: 1.3rem;
  padding: 18px 40px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(230, 126, 34, 0.3);
}

.btn-cta:hover {
  background: #cf711f;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(230, 126, 34, 0.4);
}
