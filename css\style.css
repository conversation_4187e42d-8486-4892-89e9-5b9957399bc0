/* Reset CSS cơ bản */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Nova Stars Color Palette */
  --primary-color: #2563eb;
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;
  --secondary-color: #f59e0b;
  --secondary-light: #fbbf24;
  --accent-color: #10b981;
  --accent-light: #34d399;
  --text-dark: #1f2937;
  --text-light: #6b7280;
  --background-light: #f8fafc;
  --background-cream: #fefbf7;
  --white: #ffffff;
  --border-light: #e5e7eb;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--background-cream);
}

/* Header */
.header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--white);
  padding: 1rem 0;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px var(--shadow-light);
  backdrop-filter: blur(10px);
}

.nav {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-links a {
  color: var(--white);
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
}

.nav-links a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

/* Hero Section */
.hero {
  background: linear-gradient(
    135deg,
    var(--accent-color),
    var(--primary-light)
  );
  color: var(--white);
  padding: 8rem 2rem 4rem;
  text-align: center;
  margin-top: 60px;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
  pointer-events: none;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: fadeInUp 1s ease-out;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  animation: fadeInUp 1s ease-out 0.3s both;
}

.cta-button {
  background: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--secondary-light)
  );
  color: var(--white);
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  animation: fadeInUp 1s ease-out 0.6s both;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.cta-button:hover {
  background: linear-gradient(
    135deg,
    var(--secondary-light),
    var(--secondary-color)
  );
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

/* Features Section */
.features {
  padding: 4rem 2rem;
  background: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #ff6b6b;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: #fff8f0;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  color: #ff6b6b;
  margin-bottom: 1rem;
}

/* About Section */
.about {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #e1f5fe, #f3e5f5);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.about-text h2 {
  color: #ff6b6b;
  margin-bottom: 1rem;
}

.about-image {
  text-align: center;
}

.placeholder-image {
  width: 100%;
  max-width: 400px;
  height: 300px;
  background: linear-gradient(45deg, #81c784, #64b5f6);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  margin: 0 auto;
}

/* Contact Section */
.contact {
  padding: 4rem 2rem;
  background: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-text h3 {
  color: #ff6b6b;
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

.contact-text p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.benefits-list {
  margin: 2rem 0;
}

.benefits-list h4 {
  color: #ff6b6b;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.benefits-list ul {
  list-style: none;
  padding: 0;
}

.benefits-list li {
  padding: 0.5rem 0;
  font-size: 1rem;
}

.contact-cta {
  font-style: italic;
  color: #666;
  margin-top: 1.5rem;
}

.contact-action {
  text-align: center;
}

.action-card {
  background: linear-gradient(135deg, #fff8f0, #f3e5f5);
  padding: 3rem 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.action-card:hover {
  transform: translateY(-5px);
}

.action-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.action-card h3 {
  color: #ff6b6b;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.action-card p {
  margin-bottom: 2rem;
  color: #666;
}

.partner-button {
  background: linear-gradient(135deg, #ff6b6b, #ffa726);
  font-size: 1.2rem;
  padding: 1.2rem 2.5rem;
  box-shadow: 0 5px 20px rgba(255, 107, 107, 0.3);
}

.partner-button:hover {
  background: linear-gradient(135deg, #ff5252, #ff9800);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

/* Footer */
.footer {
  background: #333;
  color: white;
  text-align: center;
  padding: 2rem;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .about-content {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .action-card {
    padding: 2rem 1.5rem;
  }

  .partner-button {
    font-size: 1rem;
    padding: 1rem 2rem;
  }
}
