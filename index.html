<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ch<PERSON><PERSON><PERSON><PERSON>ng <PERSON>ống Mầm Non - Nova Stars</title>
    <style>
      /* Reset CSS cơ bản */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Arial", sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #fff8f0;
      }

      /* Header */
      .header {
        background: linear-gradient(135deg, #ff6b6b, #ffa726);
        color: white;
        padding: 1rem 0;
        position: fixed;
        width: 100%;
        top: 0;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .nav {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 2rem;
      }

      .logo {
        font-size: 1.5rem;
        font-weight: bold;
      }

      .nav-links {
        display: flex;
        list-style: none;
        gap: 2rem;
      }

      .nav-links a {
        color: white;
        text-decoration: none;
        transition: color 0.3s;
      }

      .nav-links a:hover {
        color: #fff3e0;
      }

      /* Hero Section */
      .hero {
        background: linear-gradient(135deg, #81c784, #64b5f6);
        color: white;
        padding: 8rem 2rem 4rem;
        text-align: center;
        margin-top: 60px;
      }

      .hero-content {
        max-width: 800px;
        margin: 0 auto;
      }

      .hero h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        animation: fadeInUp 1s ease-out;
      }

      .hero p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        animation: fadeInUp 1s ease-out 0.3s both;
      }

      .cta-button {
        background: #ff6b6b;
        color: white;
        padding: 1rem 2rem;
        border: none;
        border-radius: 50px;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
        display: inline-block;
        animation: fadeInUp 1s ease-out 0.6s both;
      }

      .cta-button:hover {
        background: #ff5252;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
      }

      /* Features Section */
      .features {
        padding: 4rem 2rem;
        background: white;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }

      .section-title {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 3rem;
        color: #ff6b6b;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
      }

      .feature-card {
        background: #fff8f0;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
      }

      .feature-card:hover {
        transform: translateY(-5px);
      }

      .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }

      .feature-card h3 {
        color: #ff6b6b;
        margin-bottom: 1rem;
      }

      /* About Section */
      .about {
        padding: 4rem 2rem;
        background: linear-gradient(135deg, #e1f5fe, #f3e5f5);
      }

      .about-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        align-items: center;
      }

      .about-text h2 {
        color: #ff6b6b;
        margin-bottom: 1rem;
      }

      .about-image {
        text-align: center;
      }

      .placeholder-image {
        width: 100%;
        max-width: 400px;
        height: 300px;
        background: linear-gradient(45deg, #81c784, #64b5f6);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin: 0 auto;
      }

      /* Contact Section */
      .contact {
        padding: 4rem 2rem;
        background: white;
      }

      .contact-form {
        max-width: 600px;
        margin: 0 auto;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: #ff6b6b;
        font-weight: bold;
      }

      .form-group input,
      .form-group textarea {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        font-size: 1rem;
        transition: border-color 0.3s;
      }

      .form-group input:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #ff6b6b;
      }

      /* Footer */
      .footer {
        background: #333;
        color: white;
        text-align: center;
        padding: 2rem;
      }

      /* Animations */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Responsive */
      @media (max-width: 768px) {
        .nav-links {
          display: none;
        }

        .hero h1 {
          font-size: 2rem;
        }

        .about-content {
          grid-template-columns: 1fr;
        }

        .features-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <nav class="nav">
        <div class="logo">🌟 Nova Stars</div>
        <ul class="nav-links">
          <li><a href="#home">Trang chủ</a></li>
          <li><a href="#features">Chương trình</a></li>
          <li><a href="#about">Giới thiệu</a></li>
          <li><a href="#contact">Liên hệ</a></li>
        </ul>
      </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
      <div class="hero-content">
        <h1>🎈 Chương Trình Kỹ Năng Sống Mầm Non</h1>
        <p>
          Giúp bé phát triển toàn diện với những kỹ năng sống cần thiết qua các
          hoạt động vui nhộn và bổ ích
        </p>
        <a href="#contact" class="cta-button">Đăng ký ngay</a>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
      <div class="container">
        <h2 class="section-title">🎯 Chương Trình Của Chúng Tôi</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🤝</div>
            <h3>Kỹ Năng Giao Tiếp</h3>
            <p>
              Giúp bé học cách giao tiếp hiệu quả, lắng nghe và chia sẻ cảm xúc
              một cách tích cực với bạn bè và người lớn.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🧠</div>
            <h3>Tư Duy Sáng Tạo</h3>
            <p>
              Phát triển khả năng tư duy logic, sáng tạo và giải quyết vấn đề
              thông qua các trò chơi và hoạt động thú vị.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">❤️</div>
            <h3>Quản Lý Cảm Xúc</h3>
            <p>
              Dạy bé nhận biết, hiểu và quản lý cảm xúc của mình, xây dựng sự tự
              tin và khả năng thích ứng.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🌱</div>
            <h3>Kỹ Năng Tự Lập</h3>
            <p>
              Rèn luyện khả năng tự chăm sóc bản thân, tự lập trong sinh hoạt
              hàng ngày và có trách nhiệm với bản thân.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3>Sáng Tạo Nghệ Thuật</h3>
            <p>
              Khuyến khích bé thể hiện bản thân qua nghệ thuật, âm nhạc và các
              hoạt động sáng tạo đa dạng.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🏃‍♂️</div>
            <h3>Vận Động Thể Chất</h3>
            <p>
              Phát triển kỹ năng vận động, thể lực và sức khỏe thông qua các trò
              chơi và bài tập phù hợp với lứa tuổi.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
      <div class="container">
        <div class="about-content">
          <div class="about-text">
            <h2>🌟 Về Chương Trình Nova Stars</h2>
            <p>
              Chương trình Kỹ năng sống mầm non Nova Stars được thiết kế đặc
              biệt cho trẻ em từ 3-6 tuổi, giúp các bé phát triển toàn diện về
              thể chất, trí tuệ, cảm xúc và xã hội.
            </p>

            <p>
              Với phương pháp giáo dục hiện đại, chúng tôi tạo ra một môi trường
              học tập vui vẻ, an toàn và khuyến khích sự sáng tạo. Mỗi hoạt động
              đều được thiết kế để phù hợp với đặc điểm tâm lý và khả năng của
              trẻ mầm non.
            </p>

            <p><strong>Tại sao chọn Nova Stars?</strong></p>
            <ul style="margin-left: 1.5rem; margin-top: 1rem">
              <li>✨ Giáo viên được đào tạo chuyên nghiệp</li>
              <li>✨ Chương trình học được thiết kế khoa học</li>
              <li>✨ Môi trường học tập an toàn và thân thiện</li>
              <li>✨ Theo dõi sát sao sự phát triển của từng bé</li>
            </ul>
          </div>

          <div class="about-image">
            <div class="placeholder-image">
              🎈 Hình ảnh hoạt động của các bé
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
      <div class="container">
        <h2 class="section-title">📞 Liên Hệ Đăng Ký</h2>
        <form class="contact-form" id="contactForm">
          <div class="form-group">
            <label for="parentName">Họ tên phụ huynh:</label>
            <input type="text" id="parentName" name="parentName" required />
          </div>

          <div class="form-group">
            <label for="childName">Họ tên bé:</label>
            <input type="text" id="childName" name="childName" required />
          </div>

          <div class="form-group">
            <label for="childAge">Tuổi của bé:</label>
            <input
              type="number"
              id="childAge"
              name="childAge"
              min="3"
              max="6"
              required
            />
          </div>

          <div class="form-group">
            <label for="phone">Số điện thoại:</label>
            <input type="tel" id="phone" name="phone" required />
          </div>

          <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required />
          </div>

          <div class="form-group">
            <label for="message">Ghi chú (nếu có):</label>
            <textarea
              id="message"
              name="message"
              rows="4"
              placeholder="Chia sẻ mong muốn của bạn về chương trình..."
            ></textarea>
          </div>

          <button type="submit" class="cta-button">
            Gửi thông tin đăng ký
          </button>
        </form>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <p>
          &copy; 2024 Nova Stars - Chương Trình Kỹ Năng Sống Mầm Non. Tất cả
          quyền được bảo lưu.
        </p>
        <p>
          📍 Địa chỉ: [Địa chỉ của bạn] | 📞 Hotline: [Số điện thoại] | ✉️
          Email: [Email liên hệ]
        </p>
      </div>
    </footer>

    <script>
      // JavaScript cơ bản cho trang web

      // Smooth scrolling cho navigation links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Xử lý form liên hệ
      document
        .getElementById("contactForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          // Lấy dữ liệu từ form
          const formData = new FormData(this);
          const data = {};
          for (let [key, value] of formData.entries()) {
            data[key] = value;
          }

          // Hiển thị thông báo thành công
          alert(
            "Cảm ơn bạn đã đăng ký! Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất."
          );

          // Reset form
          this.reset();

          // Trong thực tế, bạn sẽ gửi dữ liệu này đến server
          console.log("Dữ liệu đăng ký:", data);
        });

      // Hiệu ứng khi scroll
      window.addEventListener("scroll", function () {
        const header = document.querySelector(".header");
        if (window.scrollY > 100) {
          header.style.background = "linear-gradient(135deg, #ff6b6b, #ffa726)";
          header.style.boxShadow = "0 2px 20px rgba(0,0,0,0.2)";
        } else {
          header.style.boxShadow = "0 2px 10px rgba(0,0,0,0.1)";
        }
      });

      // Animation cho feature cards khi scroll vào view
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver(function (entries) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.animation = "fadeInUp 0.6s ease-out forwards";
          }
        });
      }, observerOptions);

      // Quan sát tất cả feature cards
      document.querySelectorAll(".feature-card").forEach((card) => {
        observer.observe(card);
      });
    </script>
  </body>
</html>
