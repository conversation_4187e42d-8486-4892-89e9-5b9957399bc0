// Đ<PERSON><PERSON> bảo trang luôn tải phiên bản mới nhất
(function () {
  // Xóa cache của Service Worker nếu có
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker.getRegistrations().then(function (registrations) {
      for (let registration of registrations) {
        registration.unregister();
      }
    });
  }

  // Đảm bảo trang luôn tải từ đầu khi refresh
  window.onbeforeunload = function () {
    window.scrollTo(0, 0);
  };

  // Đảm bảo trang cuộn về đầu khi tải lại
  window.addEventListener("load", function () {
    setTimeout(function () {
      window.scrollTo(0, 0);
    }, 0);
  });
})();

// Thêm hàm để xóa cache khi cần thiết
function clearBrowserCache() {
  // Xóa cache của trình duyệt
  if ("caches" in window) {
    caches.keys().then(function (cacheNames) {
      cacheNames.forEach(function (cacheName) {
        caches.delete(cacheName);
      });
    });
  }

  // Xóa localStorage
  localStorage.clear();

  // Xóa sessionStorage
  sessionStorage.clear();

  // Tải lại trang từ server
  window.location.reload(true);
}

// Xử lý menu mobile
const mobileNavBtn = document.querySelector(".mobile-nav-btn");
const nav = document.querySelector("nav");

if (mobileNavBtn) {
  mobileNavBtn.addEventListener("click", function () {
    nav.classList.toggle("active");

    // Thêm log để debug
    console.log("Menu button clicked, nav classes:", nav.className);

    // Thêm aria-expanded để hỗ trợ accessibility
    const expanded = nav.classList.contains("active");
    this.setAttribute("aria-expanded", expanded);
  });

  // Đóng menu khi click bên ngoài
  document.addEventListener("click", function (event) {
    if (
      !nav.contains(event.target) &&
      !mobileNavBtn.contains(event.target) &&
      nav.classList.contains("active")
    ) {
      nav.classList.remove("active");
      mobileNavBtn.setAttribute("aria-expanded", "false");
    }
  });
}

// Đóng menu khi nhấn Escape
document.addEventListener("keydown", function (event) {
  if (event.key === "Escape" && nav.classList.contains("active")) {
    nav.classList.remove("active");
    mobileNavBtn.setAttribute("aria-expanded", "false");
  }
});

// Xử lý hiệu ứng cuộn mượt cho menu
document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
  anchor.addEventListener("click", function (e) {
    e.preventDefault();

    // Đóng menu mobile nếu đang mở
    if (nav.classList.contains("active")) {
      nav.classList.remove("active");
    }

    const targetId = this.getAttribute("href");
    const targetElement = document.querySelector(targetId);

    if (targetElement) {
      window.scrollTo({
        top: targetElement.offsetTop - 80,
        behavior: "smooth",
      });
    }
  });
});

// Xử lý hiệu ứng đếm số với định dạng số Việt Nam (dùng dấu . cho phần nghìn)
function animateCounter() {
  const statElements = document.querySelectorAll(".stat-number");

  statElements.forEach((stat) => {
    const target = parseInt(stat.getAttribute("data-count"));
    const duration = 2000; // 2 giây
    const step = target / (duration / 16); // 16ms là khoảng thời gian giữa các frame
    let current = 0;
    const showPlus = stat.getAttribute("data-plus") === "true";

    const updateCounter = () => {
      current += step;
      if (current < target) {
        // Sử dụng dấu . cho phần nghìn theo chuẩn Việt Nam
        stat.textContent =
          Math.floor(current)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ".") + (showPlus ? "+" : "");
        requestAnimationFrame(updateCounter);
      } else {
        // Sử dụng dấu . cho phần nghìn theo chuẩn Việt Nam
        stat.textContent =
          target.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") +
          (showPlus ? "+" : "");
      }
    };

    updateCounter();
  });
}

// Kiểm tra khi phần tử hiển thị trong viewport
function isElementInViewport(el) {
  const rect = el.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <=
      (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

// Xử lý hiệu ứng khi cuộn trang
let hasAnimated = false;
window.addEventListener("scroll", () => {
  if (!hasAnimated && isElementInViewport(document.querySelector(".stats"))) {
    animateCounter();
    hasAnimated = true;
  }
});

// Hiệu ứng sticky header
window.addEventListener("scroll", function () {
  const header = document.querySelector("header");
  header.classList.toggle("sticky", window.scrollY > 0);
});

// Hiệu ứng hiển thị các phần tử khi cuộn
const allSections = document.querySelectorAll("section");

const revealSection = function (entries, observer) {
  const [entry] = entries;

  if (!entry.isIntersecting) return;

  entry.target.classList.remove("section-hidden");
  observer.unobserve(entry.target);
};

const sectionObserver = new IntersectionObserver(revealSection, {
  root: null,
  threshold: 0.15,
});

allSections.forEach(function (section) {
  sectionObserver.observe(section);
  section.classList.add("section-hidden");
});

// Hiệu ứng hover cho các phần tử
const programItems = document.querySelectorAll(".program-item");
const benefitItems = document.querySelectorAll(".benefit-item");

// Thêm hiệu ứng hover cho program items
programItems.forEach((item) => {
  item.addEventListener("mouseenter", function () {
    this.style.transform = "translateY(-10px)";
    this.style.boxShadow = "0 15px 30px rgba(0, 0, 0, 0.15)";
  });

  item.addEventListener("mouseleave", function () {
    this.style.transform = "translateY(0)";
    this.style.boxShadow = "0 5px 15px rgba(0, 0, 0, 0.1)";
  });
});

// Thêm hiệu ứng hover cho benefit items
benefitItems.forEach((item) => {
  item.addEventListener("mouseenter", function () {
    this.style.transform = "translateY(-10px)";
    this.style.boxShadow = "0 15px 30px rgba(0, 0, 0, 0.15)";
  });

  item.addEventListener("mouseleave", function () {
    this.style.transform = "translateY(0)";
    this.style.boxShadow = "0 5px 15px rgba(0, 0, 0, 0.1)";
  });
});

// Thêm hiệu ứng hover cho contact card
document.addEventListener("DOMContentLoaded", function () {
  const contactCard = document.querySelector(".contact-card");

  if (contactCard) {
    contactCard.addEventListener("mouseenter", function () {
      this.style.transform = "translateY(-10px)";
      this.style.boxShadow = "0 15px 40px rgba(0, 0, 0, 0.15)";
    });

    contactCard.addEventListener("mouseleave", function () {
      this.style.transform = "translateY(0)";
      this.style.boxShadow = "0 10px 30px rgba(0, 0, 0, 0.1)";
    });
  }

  // Thêm hiệu ứng cho các nút liên hệ
  const contactButtons = document.querySelectorAll(".btn-contact");

  contactButtons.forEach((button) => {
    button.addEventListener("mouseenter", function () {
      this.style.transform = "translateY(-3px)";
      if (this.classList.contains("phone")) {
        this.style.boxShadow = "0 5px 15px rgba(230, 126, 34, 0.3)";
      } else if (this.classList.contains("zalo")) {
        this.style.boxShadow = "0 5px 15px rgba(0, 104, 255, 0.3)";
      } else if (this.classList.contains("email")) {
        this.style.boxShadow = "0 5px 15px rgba(39, 174, 96, 0.3)";
      }
    });

    button.addEventListener("mouseleave", function () {
      this.style.transform = "translateY(0)";
      this.style.boxShadow = "none";
    });
  });
}); // Đảm bảo UhChat được tải đúng cách
function loadUhChat() {
  // Kiểm tra nếu script UhChat chưa được tải
  if (typeof UhChat === "undefined") {
    // Tạo script element mới
    const uhchatScript = document.createElement("script");
    uhchatScript.src = "https://uhchat.net/code.php?f=00afe7";
    uhchatScript.async = true;

    // Xử lý sự kiện khi script tải xong
    uhchatScript.onload = function () {
      console.log("UhChat loaded successfully");
    };

    // Xử lý sự kiện khi script không tải được
    uhchatScript.onerror = function () {
      console.error("Failed to load UhChat widget");
    };

    // Thêm script vào cuối body
    document.body.appendChild(uhchatScript);
  }
}

// Gọi hàm tải UhChat sau khi trang đã tải hoàn toàn
window.addEventListener("load", function () {
  // Đợi 2 giây để đảm bảo trang đã tải xong hoàn toàn
  setTimeout(loadUhChat, 2000);
});

// Thêm hiệu ứng cho các icon liên hệ mới
document.addEventListener("DOMContentLoaded", function () {
  const contactIcons = document.querySelectorAll(".contact-icon");

  contactIcons.forEach((icon) => {
    icon.addEventListener("mouseenter", function () {
      this.style.transform = "scale(1.1)";
    });

    icon.addEventListener("mouseleave", function () {
      this.style.transform = "scale(1)";
    });
  });
}); // Đảm bảo UhChat được tải đúng cách
function loadUhChat() {
  // Kiểm tra nếu script UhChat chưa được tải
  if (typeof UhChat === "undefined") {
    // Tạo script element mới
    const uhchatScript = document.createElement("script");
    uhchatScript.src = "https://uhchat.net/code.php?f=00afe7";
    uhchatScript.async = true;

    // Xử lý sự kiện khi script tải xong
    uhchatScript.onload = function () {
      console.log("UhChat loaded successfully");
    };

    // Xử lý sự kiện khi script không tải được
    uhchatScript.onerror = function () {
      console.error("Failed to load UhChat widget");
    };

    // Thêm script vào cuối body
    document.body.appendChild(uhchatScript);
  }
}

// Gọi hàm tải UhChat sau khi trang đã tải hoàn toàn
window.addEventListener("load", function () {
  // Đợi 2 giây để đảm bảo trang đã tải xong hoàn toàn
  setTimeout(loadUhChat, 2000);
});
