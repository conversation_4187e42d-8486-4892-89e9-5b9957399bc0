// Phiên bản cache - thay đổi khi cập nhật website
const CACHE_VERSION = "v1.0.2";
const CACHE_NAME = "novastars-cache-" + CACHE_VERSION;

// Danh sách tài nguyên cần cache
const urlsToCache = [
  "/",
  "/index.html",
  "/css/styles.css",
  "/css/queries.css",
  "/js/script.js",
  "/img/nvs-logo-ngang.png",
  "/img/hero-bg.jpg",
];

// Cài đặt Service Worker
self.addEventListener("install", function (event) {
  event.waitUntil(
    caches.open(CACHE_NAME).then(function (cache) {
      return cache.addAll(urlsToCache);
    })
  );
});

// Kích hoạt Service Worker và xóa cache cũ
self.addEventListener("activate", function (event) {
  event.waitUntil(
    caches.keys().then(function (cacheNames) {
      return Promise.all(
        cacheNames.map(function (cacheName) {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// X<PERSON> lý fetch request
self.addEventListener("fetch", function (event) {
  // Kiểm tra nếu request là HTML hoặc có tham số no-cache
  if (
    event.request.url.includes("no-cache") ||
    event.request.url.endsWith(".html") ||
    event.request.url === self.location.origin + "/"
  ) {
    // Luôn tải từ network và cập nhật cache
    event.respondWith(
      fetch(event.request)
        .then(function (response) {
          // Kiểm tra nếu response hợp lệ
          if (
            !response ||
            response.status !== 200 ||
            response.type !== "basic"
          ) {
            return response;
          }

          // Clone response để cache
          var responseToCache = response.clone();

          caches.open(CACHE_NAME).then(function (cache) {
            cache.put(event.request, responseToCache);
          });

          return response;
        })
        .catch(function () {
          // Nếu network fail, trả về từ cache
          return caches.match(event.request);
        })
    );
  } else {
    // Với các tài nguyên khác, ưu tiên cache trước
    event.respondWith(
      caches.match(event.request).then(function (response) {
        // Trả về từ cache nếu có
        if (response) {
          return response;
        }

        // Nếu không có trong cache, tải từ network
        return fetch(event.request).then(function (response) {
          // Kiểm tra nếu response hợp lệ
          if (
            !response ||
            response.status !== 200 ||
            response.type !== "basic"
          ) {
            return response;
          }

          // Clone response để cache
          var responseToCache = response.clone();

          caches.open(CACHE_NAME).then(function (cache) {
            cache.put(event.request, responseToCache);
          });

          return response;
        });
      })
    );
  }
});
