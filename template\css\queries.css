/* rem and em do NOT depend on html font-size in media queries! Instead, 1rem = 1em = 16px */

/**************************/
/* BELOW 1344px (Smaller desktops) */
/**************************/

@media (max-width: 84em) {
  .hero h1 {
    font-size: 4rem;
  }

  .container {
    max-width: 120rem;
  }
}

/**************************/
/* BELOW 1200px (Landscape Tablets) */
/**************************/

@media (max-width: 75em) {
  .hero h1 {
    font-size: 3.6rem;
  }

  .hero p {
    font-size: 1.5rem;
  }

  .container {
    padding: 0 3.2rem;
  }

  .programs-container {
    gap: 4.8rem;
  }

  .program-item h3 {
    font-size: 1.8rem;
  }

  .program-features li {
    font-size: 1.6rem;
  }

  .stat-number {
    font-size: 3.2rem;
  }

  .stat-label {
    font-size: 1.4rem;
  }
}

/**************************/
/* BELOW 944px (Tablets) */
/**************************/

@media (max-width: 59em) {
  .hero h1 {
    font-size: 3.2rem;
  }

  .hero p {
    font-size: 1.4rem;
  }

  .hero {
    height: 100vh;
  }

  .programs-container {
    grid-template-columns: 1fr;
    gap: 6.4rem;
  }

  .stats-container {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 4rem;
  }

  .benefits-container {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 4.8rem;
  }

  .mobile-nav-btn {
    display: block;
    z-index: 9999;
    cursor: pointer;
  }

  nav {
    background-color: rgba(255, 255, 255, 0.97);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    pointer-events: none;
    visibility: hidden;
    transition: all 0.5s;
    transform: translateX(100%);
    z-index: 9998;
  }

  nav.active {
    opacity: 1;
    pointer-events: auto;
    visibility: visible;
    transform: translateX(0);
  }

  nav ul {
    flex-direction: column;
    gap: 3.2rem; /* Giảm khoảng cách giữa các mục menu */
  }

  nav ul li a {
    font-size: 2.4rem; /* Giảm kích thước chữ từ 3rem xuống 2.4rem */
    font-weight: 500;
  }

  .program-item h3 {
    font-size: 2rem;
  }

  .program-features li {
    font-size: 1.6rem;
  }

  .stat-number {
    font-size: 3rem;
  }

  .stat-label {
    font-size: 1.4rem;
  }

  .benefit-item h3 {
    font-size: 1.8rem;
  }

  .benefit-item p {
    font-size: 1.6rem;
  }
}

/**************************/
/* BELOW 704px (Smaller tablets) */
/**************************/

@media (max-width: 44em) {
  .hero h1 {
    font-size: 2.8rem;
  }

  .hero p {
    font-size: 1.3rem;
  }

  .benefits-container {
    grid-template-columns: 1fr;
  }

  .contact-card {
    flex-direction: column;
    max-width: 50rem;
  }

  .contact-image {
    padding: 3.2rem;
    width: 100%;
  }

  .contact-image i {
    font-size: 4.8rem;
  }

  .program-item h3 {
    font-size: 1.8rem;
  }

  .program-features li {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 2.8rem;
  }

  .stat-label {
    font-size: 1.3rem;
  }

  .benefit-item h3 {
    font-size: 1.7rem;
  }

  .benefit-item p {
    font-size: 1.5rem;
  }

  .contact-info h3 {
    font-size: 2.2rem;
  }

  .contact-title {
    font-size: 1.6rem;
  }

  .contact-text {
    font-size: 1.5rem;
  }
}

/**************************/
/* BELOW 544px (Phones) */
/**************************/

@media (max-width: 34em) {
  .hero h1 {
    font-size: 2.4rem;
  }

  .hero p {
    font-size: 1.2rem;
  }

  .hero-btns {
    flex-direction: column;
    gap: 1.6rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }

  .contact-form {
    padding: 3.2rem;
  }

  .btn {
    padding: 1.2rem 2.4rem;
    font-size: 1.6rem;
  }

  .contact-icon img {
    width: 3.2rem;
    height: 3.2rem;
  }

  .contact-details li {
    margin-bottom: 2.4rem;
  }

  .contact-text {
    font-size: 1.4rem;
  }

  .contact-info {
    padding: 3.2rem;
  }

  .program-item {
    padding: 3.2rem;
  }

  .program-features li {
    font-size: 1.4rem;
  }

  .stat-item {
    padding: 2.4rem;
  }

  .stat-number {
    font-size: 2.6rem;
  }

  .stat-label {
    font-size: 1.2rem;
  }

  .benefit-item {
    padding: 2.4rem;
  }

  .benefit-item i {
    font-size: 3.6rem;
  }

  .benefit-item h3 {
    font-size: 1.6rem;
  }

  .benefit-item p {
    font-size: 1.4rem;
  }

  .btn-primary {
    font-size: 1.6rem;
    padding: 1.6rem 3.2rem;
  }

  .contact-info h3 {
    font-size: 2rem;
  }

  .contact-title {
    font-size: 1.5rem;
  }
}

/**************************/
/* BELOW 375px (Smaller phones) */
/**************************/

@media (max-width: 23.44em) {
  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1.1rem;
  }

  .program-item {
    padding: 2.4rem;
  }

  .program-icon {
    font-size: 3.6rem;
    height: 6.4rem;
  }

  .program-item h3 {
    font-size: 1.5rem;
  }

  .program-features li {
    font-size: 1.3rem;
  }

  .contact-info h3 {
    font-size: 1.8rem;
  }

  .contact-title {
    font-size: 1.4rem;
  }

  .contact-icon img {
    width: 2.8rem;
    height: 2.8rem;
  }

  .contact-text {
    font-size: 1.3rem;
  }

  .stat-number {
    font-size: 2.4rem;
  }

  .stat-label {
    font-size: 1.1rem;
  }

  .benefit-item h3 {
    font-size: 1.5rem;
  }

  .benefit-item p {
    font-size: 1.3rem;
  }

  .btn-primary {
    font-size: 1.5rem;
    padding: 1.4rem 2.8rem;
  }

  .btn {
    font-size: 1.4rem;
  }
}
