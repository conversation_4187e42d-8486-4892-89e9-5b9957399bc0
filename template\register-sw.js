// Đ<PERSON>ng ký Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', function() {
    navigator.serviceWorker.register('/service-worker.js')
      .then(function(registration) {
        console.log('Service Worker đăng ký thành công:', registration.scope);
        
        // Kiểm tra cập nhật
        registration.addEventListener('updatefound', function() {
          const newWorker = registration.installing;
          
          newWorker.addEventListener('statechange', function() {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // <PERSON><PERSON> phiên bản mới, thông báo người dùng
              if (confirm('<PERSON><PERSON> phiên bản mới của trang web. Bạn có muốn tải lại trang để cập nhật?')) {
                window.location.reload();
              }
            }
          });
        });
      })
      .catch(function(error) {
        console.log('Đăng ký Service Worker thất bại:', error);
      });
      
    // <PERSON><PERSON><PERSON> tra cập nhật mỗi giờ
    setInterval(function() {
      navigator.serviceWorker.getRegistration().then(function(registration) {
        if (registration) {
          registration.update();
        }
      });
    }, 3600000); // 1 giờ
  });
}