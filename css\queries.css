/* ===========================
   RESPONSIVE QUERIES
   =========================== */

/* Tablet Landscape */
@media (max-width: 1024px) {
  .hero h1 {
    font-size: var(--font-size-3xl);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .benefits-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .survey-images {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .survey-img {
    height: 350px;
  }

  .foundation-skills-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .foundation-skills-card .skills-list {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

/* Tablet Portrait */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .nav-links {
    display: none;
  }

  .hero {
    padding: calc(60px + var(--spacing-2xl)) 0 var(--spacing-2xl);
  }

  .hero h1 {
    font-size: var(--font-size-2xl);
  }

  .hero-description {
    font-size: var(--font-size-base);
  }

  .section-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-lg);
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .survey-images {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .survey-img {
    height: 400px;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1.1rem;
  }

  .school-testimonials {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .specialized-skills {
    grid-template-columns: 1fr;
  }

  .carousel-img {
    width: 250px;
    height: 150px;
  }

  .cta-title {
    font-size: var(--font-size-xl);
  }

  .btn-cta {
    font-size: var(--font-size-lg);
    padding: var(--spacing-md) var(--spacing-xl);
  }
}

/* Mobile */
@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-xs);
  }

  .logo-img {
    height: 32px;
  }

  .hero {
    padding: calc(60px + var(--spacing-lg)) 0 var(--spacing-lg);
  }

  .hero h1 {
    font-size: var(--font-size-xl);
    line-height: 1.3;
  }

  .hero-description {
    font-size: var(--font-size-sm);
  }

  .section-title {
    font-size: var(--font-size-lg);
  }

  .feedback-subtitle {
    font-size: var(--font-size-lg);
  }

  .skill-group-title {
    font-size: var(--font-size-lg);
  }

  .benefit-group,
  .skill-group {
    padding: var(--spacing-lg);
  }

  .stat-number {
    font-size: var(--font-size-2xl);
  }

  .carousel-img {
    width: 200px;
    height: 120px;
  }

  .cta-title {
    font-size: var(--font-size-lg);
  }

  .btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
  }

  .btn-cta {
    font-size: var(--font-size-base);
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .testimonial-item {
    padding: 20px;
  }

  .testimonial-item blockquote {
    font-size: 1rem;
  }

  .testimonial-item cite {
    font-size: 0.85rem;
  }
}

/* Animation Utilities */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .carousel-track {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .header,
  .nav-links,
  .btn,
  .carousel-track {
    display: none !important;
  }

  .hero {
    background: none !important;
    color: black !important;
  }

  .section-title {
    color: black !important;
  }

  .benefit-group,
  .skill-group {
    box-shadow: none !important;
    border: 1px solid #ccc;
  }
}
